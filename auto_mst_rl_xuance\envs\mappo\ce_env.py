"""
Chase-Escape (CE) 场景环境类
实现对CE场景的强化学习环境封装
"""

import matplotlib.pyplot as plt
import numpy as np
from gym import spaces

from auto_mst_rl_xuance.envs.base.mst_env import MSTEnv
from auto_mst_rl_xuance.envs.sim_py.ce.algo.swarm_alg_ce import swarm_alg_ce
from auto_mst_rl_xuance.envs.sim_py.ce.tools.agent_utils import get_topology_neighbors
from auto_mst_rl_xuance.envs.sim_py.ce.tools.init_simulation import (
    create_initial_runtime_state,
    init_sim_robots,
)
from auto_mst_rl_xuance.envs.sim_py.ce.tools.scenario_manager import ScenarioManager
from auto_mst_rl_xuance.envs.sim_py.ce.tools.simulation import parallel_sim_robots
from auto_mst_rl_xuance.envs.sim_py.ce.tools.visualization import (
    real_time_visualization,
)

# 确保能够正确导入sim_py_copy模块 (注：此段sys.path修改已移除，采用绝对导入)
# current_dir = os.path.dirname(os.path.abspath(__file__))
# parent_dir = os.path.dirname(current_dir)
# sys.path.append(parent_dir)


class CEEnv(MSTEnv):
    """
    Chase-Escape场景环境类
    实现对CE场景的强化学习环境封装
    """

    def __init__(
        self,
        config: dict,
        max_episode_steps: int = 1200,
        enable_visualization: bool = False,
        evaluation_mode: bool = False,  # 新增: 评估模式标志
    ):
        """
        初始化CE环境

        参数:
            config (dict): 环境配置，包含仿真参数和奖励设置
            max_episode_steps (int): 最大步数
            enable_visualization (bool): 是否启用实时可视化
            evaluation_mode (bool): 是否为评估模式
        """
        self.evaluation_mode = evaluation_mode
        self.scenario_manager = None

        # 检查是否启用场景管理器
        if config.scenario_manager["enabled"]:
            # 将主配置对象传递给管理器，以便其生成场景
            self.scenario_manager = ScenarioManager(
                save_path=config.scenario_manager["scenarios_save_path"],
                num_train_scenarios=config.scenario_manager["num_train_scenarios"],
                num_eval_scenarios=config.scenario_manager["num_eval_scenarios"],
                runtime_config=config,
            )

        # 初始化基类
        super(CEEnv, self).__init__(config, max_episode_steps)
        # 设置场景类型编码
        self.scenario_type_code = 0  # Chase-Escape场景

        # 实时可视化相关设置
        self.enable_visualization = enable_visualization
        self.visualization_fig = None
        self.visualization_axes = None

        # 初始化仿真环境
        self._reset_simulation()

        # 确保agents列表在初始化时正确设置
        if not hasattr(self, "agents") or not self.agents:
            self.agents = [str(i) for i in self.runtime_state.robots_list]

        # 确保n_agents属性正确设置
        if not hasattr(self, "n_agents") or self.n_agents == 0:
            self.n_agents = len(self.agents)

        # 构建观察和动作空间 - 使用有界范围
        for agent_id in self.agents:
            self.observation_space[agent_id] = spaces.Box(
                low=-10.0, high=10.0, shape=(self._get_obs_dim(),)
            )
            self.action_space[agent_id] = spaces.Box(
                low=self.mst_min, high=self.mst_max, shape=(1,)
            )

    def _reset_simulation(self):
        """
        重置CE仿真环境
        - 如果启用了场景管理器，则从管理器加载场景
        - 否则，进行随机初始化
        """
        # 优先使用场景管理器
        if self.scenario_manager:
            # 1. 创建基础运行时状态(不含actors)
            self.runtime_state = create_initial_runtime_state(self.config)

            # 2. 从管理器获取预设的actors状态
            if self.evaluation_mode:
                # print("DEBUG: 获取评估场景...")
                actors = self.scenario_manager.get_evaluation_scenario()
            else:
                # print("DEBUG: 获取训练场景...")
                actors = self.scenario_manager.get_training_scenario()

            # 3. 加载场景
            self.runtime_state.actors = actors
            self.runtime_state.robots_list = list(actors.keys())
            return  # 直接返回，跳过下面的随机初始化

        # --- 如果未使用场景管理器，则执行原有的随机初始化逻辑 ---
        # 获取配置参数
        max_id = getattr(self.config, "max_id", 50)

        # 初始化运行时状态
        self.runtime_state = create_initial_runtime_state(self.config)

        # 初始化虚拟智能体
        self.runtime_state = init_sim_robots(
            self.runtime_state,
            "unif",  # 均匀分布初始化
            np.array([0, 0]),  # 中心位置
            100,  # 分布半径
            True,  # 并行仿真
        )

    def reset(self):
        """
        重置环境，在父类方法基础上增加打印信息

        返回:
            observations (dict): 各智能体的初始观察
            reset_info (dict): 重置信息
        """
        # print("CEEnv: 开始新的episode")
        # 重置可视化
        if self.enable_visualization:
            if self.visualization_fig is not None:
                plt.close(self.visualization_fig)
            self.visualization_fig = None
            self.visualization_axes = None

        observations, reset_info = super().reset()

        # 重置后立即显示初始状态
        if self.enable_visualization:
            # 获取平均MST值作为初始显示
            mst_values = []
            for agent_id in self.agents:
                agent = self.runtime_state.actors[int(agent_id)]
                if hasattr(agent, "cj_threshold"):
                    mst_values.append(agent.cj_threshold)
            avg_mst = np.mean(mst_values) if mst_values else 0.0

            self.visualization_fig, self.visualization_axes = real_time_visualization(
                self.runtime_state,
                enable=True,
                accumulated_reward=0.0,
                mst_value=avg_mst,
                fig=self.visualization_fig,
                ax=self.visualization_axes,
            )

        return observations, reset_info

    def _simulation_step(self):
        """
        执行CE仿真步进
        """
        # 更新仿真步骤计数器
        self.runtime_state.sim_step = self.episode_step + 1

        # 计算期望转向角度和速度
        des_turn_angle, des_speed, self.runtime_state = swarm_alg_ce(self.runtime_state)

        # 更新智能体状态
        self.runtime_state = parallel_sim_robots(
            self.runtime_state, des_turn_angle, des_speed
        )

    def step(self, actions):
        """
        执行环境步进，在父类方法基础上增加打印信息

        参数:
            actions (dict): 各智能体的动作

        返回:
            observations (dict): 下一状态的观察
            rewards (dict): 各智能体的奖励
            terminated (dict): 各智能体是否终止
            truncated (dict): 是否因达到最大步数而截断
            info (dict): 附加信息
        """

        # 调用父类的step方法
        observations, rewards, terminated, truncated, info = super().step(actions)

        # 打印终止或截断信息
        # any_terminated = any(terminated.values())
        # if any_terminated or truncated:
        #     if any_terminated:
        #         print(f"CEEnv: 在第{self.episode_step}步终止，由于终止条件满足")
        #     if truncated:
        #         print(f"CEEnv: 在第{self.episode_step}步截断，达到最大步数")
        #     # 打印最终平均奖励
        #     avg_reward = np.mean(list(self.individual_episode_reward.values()))
        #     print(f"CEEnv: Episode完成，平均累积奖励: {avg_reward:.4f}")

        return observations, rewards, terminated, truncated, info

    def _get_agent_obs(self, agent_id):
        """
        获取CE场景中单个智能体的观察

        参数:
            agent_id (str): 智能体ID

        返回:
            observation (np.ndarray): 智能体观察
        """
        actual_id = int(agent_id)
        agent = self.runtime_state.actors[actual_id]

        # 获取邻居
        neighbors, num_neighbors, neighbors_id_list = get_topology_neighbors(
            self.runtime_state, actual_id
        )

        # A. 自身状态 (4维)
        # 归一化速度向量、速度大小、是否被激活、当前阈值
        vel_magnitude = np.linalg.norm(agent.vel)
        if vel_magnitude > 0:
            norm_vx, norm_vy = agent.vel / vel_magnitude
        else:
            norm_vx, norm_vy = 0.0, 0.0

        self_state = np.array(
            [
                norm_vx,
                norm_vy,
                vel_magnitude / self.runtime_state.config.v0,  # 归一化速度大小
                1.0 if agent.is_activated else 0.0,  # 是否被激活
            ]
        )

        # B. 邻居Mj统计 (3维)
        if (
            num_neighbors > 0
            and hasattr(agent, "observed_neighbor_cj_values")
            and len(agent.observed_neighbor_cj_values) > 0
        ):
            max_mj = np.max(agent.observed_neighbor_cj_values)
            avg_mj = np.mean(agent.observed_neighbor_cj_values)
            var_mj = np.var(agent.observed_neighbor_cj_values)
        else:
            max_mj, avg_mj, var_mj = 0.0, 0.0, 0.0

        neighbor_stats = np.array(
            [
                np.clip(
                    max_mj / self.mst_max if self.mst_max > 0 else max_mj, 0.0, 1.0
                ),  # 归一化最大Mj，并裁剪到[0,1]
                np.clip(
                    avg_mj / self.mst_max if self.mst_max > 0 else avg_mj, 0.0, 1.0
                ),  # 归一化平均Mj，并裁剪到[0,1]
                np.clip(
                    var_mj / (self.mst_max**2) if self.mst_max > 0 else var_mj, 0.0, 1.0
                ),  # 归一化方差，并裁剪到[0,1]
            ]
        )

        # C. k个排序邻居的详细信息 (5k维)
        neighbor_details = np.zeros(5 * self.k_neighbors)

        if num_neighbors > 0 and hasattr(agent, "observed_neighbor_cj_values"):
            # 对邻居按Mj值排序
            if len(agent.observed_neighbor_cj_values) > 0:
                sorted_indices = np.argsort(
                    -np.array(agent.observed_neighbor_cj_values)
                )

                for i in range(min(self.k_neighbors, len(sorted_indices))):
                    idx = sorted_indices[i]
                    if idx < len(neighbors_id_list):
                        nei_id = neighbors_id_list[idx]
                        nei_agent = self.runtime_state.actors[nei_id]

                        # 相对位置
                        rel_pos = nei_agent.pose - agent.pose
                        rel_pos_norm = np.linalg.norm(rel_pos)
                        if rel_pos_norm > 0:
                            rel_pos = rel_pos / rel_pos_norm

                        # 相对速度
                        rel_vel = nei_agent.vel - agent.vel
                        rel_vel_norm = np.linalg.norm(rel_vel)
                        if rel_vel_norm > 0:
                            rel_vel = rel_vel / rel_vel_norm

                        # Mj值 - 确保归一化并裁剪
                        mj_val = agent.observed_neighbor_cj_values[idx]
                        mj = np.clip(
                            mj_val / self.mst_max if self.mst_max > 0 else mj_val,
                            0.0,
                            1.0,
                        )

                        # 填充到邻居详情中
                        neighbor_details[i * 5 : i * 5 + 5] = [
                            rel_pos[0],  # 已归一化
                            rel_pos[1],  # 已归一化
                            rel_vel[0],  # 已归一化
                            rel_vel[1],  # 已归一化
                            mj,  # 已归一化并裁剪
                        ]

        # D. 场景与任务信息 (5维)
        scenario_info = np.zeros(5)
        scenario_info[0] = self.scenario_type_code  # 场景信息

        # CE场景特定信息
        hawk_exists = self.runtime_state.config.hawk_id in self.runtime_state.actors

        if hawk_exists:
            hawk = self.runtime_state.actors[self.runtime_state.config.hawk_id]
            vec_to_hawk = hawk.pose - agent.pose
            dist_to_hawk = np.linalg.norm(vec_to_hawk)

            # 初始化捕食者方向变量，避免未定义
            norm_dir_to_hawk = np.array([0.0, 0.0])

            if dist_to_hawk > 0:
                norm_dir_to_hawk = vec_to_hawk / dist_to_hawk

            # 重要改动：检查捕食者是否在智能体的探测范围内
            agent_idx = actual_id - 1
            agent_r_escape = self.runtime_state.config.r_escape[agent_idx]

            # 只有当捕食者在探测范围内时，才提供捕食者信息
            if dist_to_hawk <= agent_r_escape:
                # 填充场景信息 - 捕食者在探测范围内
                scenario_info[1] = 1.0  # 捕食者存在
                scenario_info[2] = norm_dir_to_hawk[0]  # 归一化方向x
                scenario_info[3] = norm_dir_to_hawk[1]  # 归一化方向y
                scenario_info[4] = min(1.0, dist_to_hawk / 1000.0)  # 归一化距离
            else:
                # 捕食者不在探测范围内，所有关于捕食者的信息为0
                scenario_info[1] = 0.0  # 捕食者不存在
                scenario_info[2] = 0.0  # 方向x为0
                scenario_info[3] = 0.0  # 方向y为0
                scenario_info[4] = 0.0  # 距离为0

        # 组合所有特征
        observation = np.concatenate(
            [self_state, neighbor_stats, neighbor_details, scenario_info]
        ).astype(np.float32)

        return observation

    def _compute_rewards(self):
        """
        计算CE场景的奖励

        返回:
            rewards (dict): 各智能体的奖励
        """
        # 计算全局共享奖励
        global_reward = self._compute_global_reward()

        # 为所有智能体分配相同的奖励
        rewards = {agent_id: global_reward for agent_id in self.agents}

        return rewards

    def _compute_global_reward(self):
        """
        计算CE场景的全局奖励。
        目标: 最大化第一个个体被捕获的时间。
        奖励组成:
        1. 每步生存奖励: 只要没有猎物被捕获，就给予固定正奖励。
        2. 最小安全距离奖励: 基于离捕食者最近的猎物的距离，距离越远奖励越高。
        3. 逃逸方向奖励: 鼓励群体朝正确的方向逃跑。
        返回:
            reward (float): 全局奖励
        """
        # 定义奖励参数 (这些值可以根据实验效果进行调整)
        TIME_STEP_SURVIVAL_REWARD = getattr(
            self.config, "time_step_survival_reward", 0.1
        )
        WEIGHT_MIN_DISTANCE = getattr(self.config, "weight_min_distance", 0.5)
        WEIGHT_ESCAPE_DIR = getattr(self.config, "weight_escape_dir", 0.2)
        DISTANCE_NORMALIZATION_FACTOR = getattr(
            self.config, "distance_normalization_factor", 1000.0
        )

        # 1. 每步生存奖励
        # 只要能执行到这个函数，就意味着episode还没有因为捕获而终止
        current_reward = TIME_STEP_SURVIVAL_REWARD

        # 初始化辅助奖励项
        min_dist_to_hawk_normalized = 0.0
        escape_dir_term = 0.0  # 保留原有的逃逸方向项计算逻辑

        hawk_exists = self.runtime_state.config.hawk_id in self.runtime_state.actors
        if hawk_exists:
            hawk = self.runtime_state.actors[self.runtime_state.config.hawk_id]

            # --- 计算最小安全距离 ---
            min_dist_to_hawk = float("inf")
            prey_distances_to_hawk = []  # 用于可能的其他统计，但主要关注min_dist

            active_prey_poses = []  # 用于计算群体中心和逃逸方向
            active_prey_escape_vectors = []  # 用于计算平均逃逸方向

            for prey_id_str in self.agents:  # self.agents 是字符串ID列表
                prey_id = int(prey_id_str)
                if (
                    prey_id == self.runtime_state.config.hawk_id
                    or prey_id not in self.runtime_state.robots_list
                ):  # robots_list 存活的智能体ID
                    continue

                prey = self.runtime_state.actors[prey_id]
                active_prey_poses.append(prey.pose)

                vec_to_hawk = hawk.pose - prey.pose
                distance = np.linalg.norm(vec_to_hawk)
                prey_distances_to_hawk.append(distance)

                if distance < min_dist_to_hawk:
                    min_dist_to_hawk = distance

                # 计算个体逃逸方向 (用于 escape_dir_term)
                if distance > 0:
                    escape_vec = -vec_to_hawk / distance  # 逃离方向向量 (已归一化)
                    active_prey_escape_vectors.append(escape_vec)

            if min_dist_to_hawk != float("inf"):
                min_dist_to_hawk_normalized = min(
                    1.0, min_dist_to_hawk / DISTANCE_NORMALIZATION_FACTOR
                )

            current_reward += WEIGHT_MIN_DISTANCE * min_dist_to_hawk_normalized

            # --- 计算逃逸方向项 (与原逻辑类似，但基于当前存活的猎物) ---
            if active_prey_escape_vectors and active_prey_poses:
                avg_escape_dir_vec = np.mean(active_prey_escape_vectors, axis=0)
                avg_escape_dir_norm = np.linalg.norm(avg_escape_dir_vec)

                if avg_escape_dir_norm > 0:
                    normalized_avg_escape_dir = avg_escape_dir_vec / avg_escape_dir_norm

                    swarm_center_pose = np.mean(active_prey_poses, axis=0)
                    ideal_escape_dir_vec = (
                        swarm_center_pose - hawk.pose
                    )  # 从捕食者指向群体中心的反方向
                    ideal_escape_dir_norm = np.linalg.norm(ideal_escape_dir_vec)

                    if ideal_escape_dir_norm > 0:
                        normalized_ideal_escape_dir = (
                            ideal_escape_dir_vec / ideal_escape_dir_norm
                        )
                        dot_product = np.dot(
                            normalized_avg_escape_dir, normalized_ideal_escape_dir
                        )
                        escape_dir_term = (dot_product + 1) / 2  # 归一化到 [0, 1]

            current_reward += WEIGHT_ESCAPE_DIR * escape_dir_term

        return current_reward

    def _get_termination(self):
        """
        检查CE场景是否达到终止条件

        返回:
            terminated (dict): 各智能体是否终止
        """
        # 检查是否结束（捕食者捕获猎物）
        terminated = False

        # 检查捕食者是否抓到猎物
        target_dist_current_step = np.nan
        if self.runtime_state.sim_step - 1 < len(
            self.runtime_state.metrics.target_dist
        ):
            target_dist_current_step = self.runtime_state.metrics.target_dist[
                self.runtime_state.sim_step - 1
            ]

        # 捕获条件检查
        hawk_exists = self.runtime_state.config.hawk_id in self.runtime_state.actors
        attack_step_condition = (
            self.runtime_state.sim_step >= self.runtime_state.config.attack_step
        )
        valid_dist = not np.isnan(target_dist_current_step)
        capture_condition = (
            valid_dist and target_dist_current_step <= self.runtime_state.config.r_dead
        )

        if attack_step_condition and hawk_exists and valid_dist and capture_condition:
            terminated = True

        # 同样终止所有智能体
        return {agent_id: terminated for agent_id in self.agents}

    def state(self):
        """
        返回CE场景的全局状态，用于中心化Critic的输入

        返回:
            state (np.ndarray): 全局状态表示
        """
        # 构建全局状态表示 (20维，与FPEnv保持一致)
        state = np.zeros(20)

        # 1. scenario_type_code (1维)
        state[0] = self.scenario_type_code

        # 2. normalized_current_sim_step (1维)
        state[1] = self.episode_step / self.max_episode_steps

        # 3-4. swarm_center_pos_x_norm, swarm_center_pos_y_norm (2维)
        prey_positions = np.array(
            [
                self.runtime_state.actors[i].pose
                for i in self.runtime_state.robots_list
                if i != self.runtime_state.config.hawk_id
            ]
        )
        swarm_center = np.mean(prey_positions, axis=0)
        # 归一化到[-1, 1]范围
        state[2] = swarm_center[0] / 1000.0
        state[3] = swarm_center[1] / 1000.0

        # 5-6. swarm_avg_vel_x_norm, swarm_avg_vel_y_norm (2维)
        prey_velocities = np.array(
            [
                self.runtime_state.actors[i].vel
                for i in self.runtime_state.robots_list
                if i != self.runtime_state.config.hawk_id
            ]
        )
        avg_velocity = np.mean(prey_velocities, axis=0)
        vel_magnitude = np.linalg.norm(avg_velocity)
        if vel_magnitude > 0:
            avg_velocity = avg_velocity / vel_magnitude
        state[4] = avg_velocity[0]
        state[5] = avg_velocity[1]

        # 7. swarm_spread_norm (1维)
        if len(prey_positions) > 0:
            distances_to_center = np.linalg.norm(prey_positions - swarm_center, axis=1)
            spread = np.mean(distances_to_center)
            state[6] = min(1.0, spread / 500.0)  # 归一化

        # 8. activated_agent_ratio_norm (1维)
        activated_count = sum(
            1
            for i in self.runtime_state.robots_list
            if i != self.runtime_state.config.hawk_id
            and self.runtime_state.actors[i].is_activated
        )
        activated_ratio = activated_count / max(1, len(prey_positions))
        state[7] = activated_ratio

        # 9. avg_Ci_norm (1维)
        ci_values = [
            self.runtime_state.actors[i].cj_threshold
            for i in self.runtime_state.robots_list
            if i != self.runtime_state.config.hawk_id
        ]
        avg_ci = np.mean(ci_values) if ci_values else 0
        state[8] = avg_ci / self.mst_max if self.mst_max > 0 else avg_ci  # 归一化

        # 10. std_Ci_norm (1维)
        std_ci = np.std(ci_values) if ci_values else 0
        state[9] = std_ci / self.mst_max if self.mst_max > 0 else std_ci  # 归一化

        # CE场景特定特征 (9维)
        hawk_exists = self.runtime_state.config.hawk_id in self.runtime_state.actors

        # 11. ce_predator_exists_flag (1维)
        state[10] = 1.0 if hawk_exists else 0.0

        if hawk_exists:
            hawk = self.runtime_state.actors[self.runtime_state.config.hawk_id]

            # 12-13. ce_predator_pos_x_norm, ce_predator_pos_y_norm (2维)
            state[11] = hawk.pose[0] / 1000.0
            state[12] = hawk.pose[1] / 1000.0

            # 14. ce_avg_dist_to_predator_norm (1维)
            distances_to_hawk = [
                np.linalg.norm(self.runtime_state.actors[i].pose - hawk.pose)
                for i in self.runtime_state.robots_list
                if i != self.runtime_state.config.hawk_id
            ]
            avg_dist = np.mean(distances_to_hawk) if distances_to_hawk else 0
            state[13] = min(1.0, avg_dist / 1000.0)

            # 15. ce_min_dist_to_predator_norm (1维)
            min_dist = np.min(distances_to_hawk) if distances_to_hawk else 0
            state[14] = min(1.0, min_dist / 1000.0)

            # 集中注意方向
            attention_dirs = []
            for i in self.runtime_state.robots_list:
                if i == self.runtime_state.config.hawk_id:
                    continue
                agent = self.runtime_state.actors[i]
                if agent.is_activated and agent.src_id is not None:
                    src_agent = self.runtime_state.actors[agent.src_id]
                    vec = src_agent.pose - agent.pose
                    if np.linalg.norm(vec) > 0:
                        attention_dirs.append(vec / np.linalg.norm(vec))

            # 17. ce_group_escape_alignment_norm (1维)
            if attention_dirs:
                avg_attention = np.mean(attention_dirs, axis=0)
                if np.linalg.norm(avg_attention) > 0:
                    avg_attention = avg_attention / np.linalg.norm(avg_attention)

                    # 理想的逃逸方向是远离捕食者
                    ideal_escape_dir = swarm_center - hawk.pose
                    if np.linalg.norm(ideal_escape_dir) > 0:
                        ideal_escape_dir = ideal_escape_dir / np.linalg.norm(
                            ideal_escape_dir
                        )

                        # 逃逸一致性是平均注意方向与理想逃逸方向的点积
                        alignment = np.dot(avg_attention, ideal_escape_dir)
                        state[16] = (alignment + 1) / 2  # 归一化到[0,1]

        # 16. ce_num_surviving_prey_norm (1维)
        surviving_prey_count = len(
            [
                i
                for i in self.runtime_state.robots_list
                if i != self.runtime_state.config.hawk_id
            ]
        )
        state[15] = surviving_prey_count / self.runtime_state.max_id

        # 18-20. 添加额外维度，与FPEnv保持一致（对CE场景，这些值无实际意义）
        # 这三个维度在FP场景中用于描述信息个体状态
        state[17] = 0.0  # CE中无信息个体位置偏移x
        state[18] = 0.0  # CE中无信息个体位置偏移y
        state[19] = 0.0  # CE中无信息个体旋转周期

        return state

    def render(self, mode=None):
        """
        渲染环境，实现实时可视化显示

        参数:
            mode (str): 渲染模式

        返回:
            None
        """
        if not self.enable_visualization:
            self.enable_visualization = True
            # 重置可视化状态
            if self.visualization_fig is not None:
                plt.close(self.visualization_fig)
            self.visualization_fig = None
            self.visualization_axes = None

            # 计算MST值和累积奖励
            mst_values = []
            for agent_id in self.agents:
                agent = self.runtime_state.actors[int(agent_id)]
                if hasattr(agent, "cj_threshold"):
                    mst_values.append(agent.cj_threshold)
            avg_mst = np.mean(mst_values) if mst_values else 0.0

            cumulative_reward = (
                np.mean(list(self.individual_episode_reward.values()))
                if self.individual_episode_reward
                else 0.0
            )

            # 更新可视化
            self.visualization_fig, self.visualization_axes = real_time_visualization(
                self.runtime_state,
                enable=True,
                accumulated_reward=cumulative_reward,
                mst_value=avg_mst,
                fig=self.visualization_fig,
                ax=self.visualization_axes,
            )

        return None

    def close(self):
        """
        关闭环境，关闭可视化窗口
        """
        if self.enable_visualization and self.visualization_fig is not None:
            plt.close(self.visualization_fig)
            self.visualization_fig = None
            self.visualization_axes = None
